// 导入样式变量
@use './variables';

// 导入所有核心样式
@use './base';
@use './cursor';
@use './lists';
@use './code';
@use './animations';

// 导入菜单样式
@use '../../menu/styles/menu';

// 导入扩展样式
@use '../../extensions/image/styles/image';
@use '../../extensions/image/styles/image-node-view';
@use '../../extensions/mention/styles/mention';
@use '../../extensions/bilibili/styles/bilibili';
@use '../../extensions/code-block/styles/code-block';
@use '../../extensions/slash-menu/styles' as slash-menu-styles;