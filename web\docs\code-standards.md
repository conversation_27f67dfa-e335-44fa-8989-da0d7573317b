# 项目代码规范

## 命名规范

### 文件命名

- 组件文件使用 PascalCase（大驼峰）命名，例如：`UserProfile.vue`
- 工具类文件使用 kebab-case（短横线）命名，例如：`date-utils.ts`
- 类型定义文件使用 kebab-case 命名，并以 `.types.ts` 结尾，例如：`user.types.ts`
- 样式文件使用与其关联组件相同的命名方式，例如：`UserProfile.scss`

### 变量命名

- 使用有意义的变量名，避免使用单字母变量名（循环中的 i, j 等除外）
- 布尔类型变量使用 is, has, can 等前缀，例如：`isVisible`, `hasPermission`
- 常量使用全大写，单词间用下划线分隔，例如：`MAX_COUNT`
- 类型别名使用 PascalCase，例如：`UserType`
- 枚举类型使用 PascalCase，例如：`UserRole`

## Vue 组件规范

### 组件结构

- 组件应使用 `<script setup>` 语法
- 组件结构应按照 `<template>`, `<script>`, `<style>` 的顺序排列
- 使用 `defineProps()`, `defineEmits()` 定义组件接口
- 使用 `ref`, `computed` 等 Composition API 进行状态管理

### 模板规范

- 使用 PascalCase 引用组件
- 属性应使用短横线命名法
- 复杂表达式应提取为计算属性

### 样式规范

- 样式应添加 `scoped` 或使用 CSS 模块
- 颜色值使用变量定义，避免直接使用颜色代码
- 尺寸应使用相对单位（rem, em, vw, vh）
- 使用统一的间距和字体大小变量

## TypeScript 规范

- 所有变量、参数、返回值都应有明确的类型注解
- 避免使用 `any` 类型
- 接口优先于类型别名
- 使用类型守卫进行类型断言

## 代码注释规范

- 公共方法、复杂逻辑应添加文档注释
- 使用 JSDoc 风格的注释
- 注释应解释"为什么"而不是"是什么"
- 代码提交前清理无用注释和代码

## Git 提交规范

使用 Angular 风格的提交信息：

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型（type）:
- feat: 新功能
- fix: 修复bug
- docs: 文档修改
- style: 代码格式修改
- refactor: 重构
- perf: 性能优化
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 最佳实践

- 使用 `npm run lint` 和 `npm run format` 保持代码风格一致
- 提交前运行 `npm run code-check` 确保代码质量
- 遵循 DRY（Don't Repeat Yourself）原则
- 组件和函数应保持单一职责
- 避免过深的嵌套层级 