import type { Editor } from '@tiptap/core'

import type { SlashMenuItem } from './types'

// 图片上传触发器类型
type ImageUploadTrigger = () => void

// 模态框触发器类型
type ModalTrigger = (title: string, callback: () => void, needInput?: boolean) => void

// SVG 图标定义
const icons = {
  heading1: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 12h8m-8 6V6m8 12V6m5 6l3-2v8"/></svg>`,
  heading2: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 12h8m-8 6V6m8 12V6m9 12h-4c0-4 4-3 4-6c0-1.5-2-2.5-4-1"/></svg>`,
  heading3: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 12h8m-8 6V6m8 12V6m5.5 4.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2m-2 3.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2"/></svg>`,
  bulletList: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12h.01M3 18h.01M3 6h.01M8 12h13M8 18h13M8 6h13"/></svg>`,
  orderedList: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10 12h11m-11 6h11M10 6h11M4 10h2M4 6h1v4m1 8H4c0-1 2-2 2-3s-1-1.5-2-1"/></svg>`,
  taskList: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 17l2 2l4-4M3 7l2 2l4-4m4 1h8m-8 6h8m-8 6h8"/></svg>`,
  blockquote: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1a6 6 0 0 0 6-6V5a2 2 0 0 0-2-2zM5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1a6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z"/></svg>`,
  codeBlock: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 16l4-4l-4-4M6 8l-4 4l4 4m8.5-12l-5 16"/></svg>`,
  image: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>`,
  link: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/></svg>`,
  bilibili: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 10l-6.5 6.5a2.12 2.12 0 0 1-3-3L13 7"/><path d="m16 10l2 2l-4 4"/><path d="M8 12l2-2"/></svg>`,
  bold: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/></svg>`,
  italic: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="19" y1="4" x2="10" y2="4"/><line x1="14" y1="20" x2="5" y2="20"/><line x1="15" y1="4" x2="9" y2="20"/></svg>`,
  strike: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4H9a3 3 0 0 0-2.83 4"/><path d="M14 12a4 4 0 0 1 0 8H6"/><line x1="4" y1="12" x2="20" y2="12"/></svg>`,
  underline: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"/><line x1="4" y1="21" x2="20" y2="21"/></svg>`,
  code: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="16,18 22,12 16,6"/><polyline points="8,6 2,12 8,18"/></svg>`,
  horizontalRule: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12h18M8 8l4-4l4 4m0 8l-4 4l-4-4"/></svg>`,
}

// 创建默认的斜杠菜单项
export const createDefaultSlashMenuItems = (
  imageUploadTrigger?: ImageUploadTrigger,
  modalTrigger?: ModalTrigger,
): SlashMenuItem[] => [
  {
    id: 'heading1',
    name: '标题 1',
    icon: icons.heading1,
    keywords: 'h1 标题 heading',
    shortcut: 'Ctrl+Alt+1',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    id: 'heading2',
    name: '标题 2',
    icon: icons.heading2,
    keywords: 'h2 标题 heading',
    shortcut: 'Ctrl+Alt+2',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    id: 'heading3',
    name: '标题 3',
    icon: icons.heading3,
    keywords: 'h3 标题 heading',
    shortcut: 'Ctrl+Alt+3',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 3 }).run(),
  },
  '|',
  {
    id: 'bold',
    name: '加粗',
    icon: icons.bold,
    keywords: 'bold 加粗 粗体 b',
    shortcut: 'Ctrl+B',
    action: (editor: Editor) => editor.chain().focus().toggleBold().run(),
  },
  {
    id: 'italic',
    name: '斜体',
    icon: icons.italic,
    keywords: 'italic 斜体 i',
    shortcut: 'Ctrl+I',
    action: (editor: Editor) => editor.chain().focus().toggleItalic().run(),
  },
  {
    id: 'strike',
    name: '删除线',
    icon: icons.strike,
    keywords: 'strike 删除线 strikethrough',
    action: (editor: Editor) => editor.chain().focus().toggleStrike().run(),
  },
  {
    id: 'underline',
    name: '下划线',
    icon: icons.underline,
    keywords: 'underline 下划线 u',
    shortcut: 'Ctrl+U',
    action: (editor: Editor) => editor.chain().focus().toggleUnderline().run(),
  },
  {
    id: 'code',
    name: '行内代码',
    icon: icons.code,
    keywords: 'code 代码 行内代码 inline',
    shortcut: 'Ctrl+E',
    action: (editor: Editor) => editor.chain().focus().toggleCode().run(),
  },
  '|',
  {
    id: 'bulletList',
    name: '无序列表',
    icon: icons.bulletList,
    keywords: 'ul 列表 list bullet',
    action: (editor: Editor) => editor.chain().focus().toggleBulletList().run(),
  },
  {
    id: 'orderedList',
    name: '有序列表',
    icon: icons.orderedList,
    keywords: 'ol 列表 list ordered number',
    action: (editor: Editor) => editor.chain().focus().toggleOrderedList().run(),
  },
  {
    id: 'taskList',
    name: '任务列表',
    icon: icons.taskList,
    keywords: 'todo 任务 task checklist',
    action: (editor: Editor) => editor.chain().focus().toggleTaskList().run(),
  },
  '|',
  {
    id: 'blockquote',
    name: '引用',
    icon: icons.blockquote,
    keywords: 'quote 引用 blockquote',
    action: (editor: Editor) => editor.chain().focus().toggleBlockquote().run(),
  },
  {
    id: 'codeBlock',
    name: '代码块',
    icon: icons.codeBlock,
    keywords: 'code 代码 codeblock',
    shortcut: 'Ctrl+Alt+C',
    action: (editor: Editor) => editor.chain().focus().toggleCodeBlock().run(),
  },
  '|',
  {
    id: 'image',
    name: '图片',
    icon: icons.image,
    keywords: 'image 图片 img picture',
    action: (editor: Editor) => {
      // 使用与工具栏一致的图片上传逻辑
      if (imageUploadTrigger) {
        imageUploadTrigger()
      } else {
        // 降级方案：使用简单的URL输入
        const src = prompt('请输入图片URL:')
        if (src) {
          editor.chain().focus().setImage({ src }).run()
        }
      }
    },
  },
  {
    id: 'link',
    name: '链接',
    icon: icons.link,
    keywords: 'link 链接 url',
    action: (editor: Editor) => {
      // 使用与工具栏一致的链接插入逻辑（两个输入框）
      if (modalTrigger) {
        modalTrigger('插入链接', () => {}, false)
      } else {
        // 降级方案：使用简单的URL输入
        const href = prompt('请输入链接URL:')
        if (href) {
          editor.chain().focus().setLink({ href }).run()
        }
      }
    },
  },
  {
    id: 'bilibili',
    name: 'B站视频',
    icon: icons.bilibili,
    keywords: 'bilibili b站 视频 video',
    action: (editor: Editor) => {
      // 使用与工具栏一致的B站视频插入逻辑
      if (modalTrigger) {
        modalTrigger('插入bilibili视频链接', () => {}, true)
      } else {
        // 降级方案：使用简单的URL输入
        const src = prompt('请输入B站视频链接:')
        if (src) {
          // @ts-expect-error - setBilibiliVideo is a custom command that may not be typed
          editor.commands.setBilibiliVideo({ src })
        }
      }
    },
  },
  {
    id: 'horizontalRule',
    name: '分割线',
    icon: icons.horizontalRule,
    keywords: 'hr 分割线 divider line',
    action: (editor: Editor) => editor.chain().focus().setHorizontalRule().run(),
  },
]
