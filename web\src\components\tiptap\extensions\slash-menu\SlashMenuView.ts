import type { Editor, Range } from '@tiptap/core'
import type { SuggestionKeyDownProps, SuggestionOptions, SuggestionProps } from '@tiptap/suggestion'
// @ts-ignore
import scrollIntoView from 'smooth-scroll-into-view-if-needed'

import type { SlashMenuItem, SlashMenuViewOptions } from './types'

export class SlashMenuView implements ReturnType<SuggestionOptions['render']> {
  private readonly editor: Editor
  private readonly options: SlashMenuViewOptions
  private element: HTMLElement | undefined
  private index: number | undefined
  private nodes: HTMLElement[] | undefined
  private items: SlashMenuItem[] | undefined

  public static create(options: SlashMenuViewOptions) {
    return () => new SlashMenuView(options)
  }

  constructor(options: SlashMenuViewOptions) {
    this.editor = options.editor
    this.options = options
  }

  public onStart(props: SuggestionProps) {
    // 重置状态
    this.index = 0
    this.nodes = []
    this.items = []

    // 创建根元素
    this.element = document.createElement('div')
    this.element.classList.add('slash-menu')

    // 添加自定义类名
    for (const clazz of this.options.classes ?? []) {
      this.element.classList.add(clazz)
    }

    // 添加自定义属性
    for (const [key, val] of Object.entries(this.options.attributes ?? {})) {
      this.element.setAttribute(key, val)
    }

    // 添加到页面
    document.body.appendChild(this.element)

    this.onUpdate(props)
  }

  public onUpdate(props: SuggestionProps) {
    if (!this.element || this.index === undefined || !this.nodes || !this.items) {
      return
    }

    // 更新项目
    this.items = props.items

    // 渲染菜单项
    this.render()

    // 更新位置
    this.updatePosition(props.clientRect)
  }

  public onKeyDown(props: SuggestionKeyDownProps) {
    if (!this.element || this.index === undefined || !this.nodes || !this.items) {
      return false
    }

    if (props.event.key === 'Escape') {
      this.hide()
      return true
    }

    if (props.event.key === 'Enter') {
      const item = this.items[this.index]
      if (item && typeof item !== 'string' && item.action) {
        item.action(this.editor)
      }
      return true
    }

    if (props.event.key === 'ArrowUp') {
      const prev = this.index - 1
      const index = this.items[prev] && typeof this.items[prev] === 'string' ? prev - 1 : prev
      this.selectItem(index < 0 ? this.items.length - 1 : index, true)
      return true
    }

    if (props.event.key === 'ArrowDown') {
      const next = this.index + 1
      const index = this.items[next] && typeof this.items[next] === 'string' ? next + 1 : next
      this.selectItem(index >= this.items.length ? 0 : index, true)
      return true
    }

    return false
  }

  public onExit() {
    this.hide()
    this.cleanup()
  }

  private selectItem(index: number, scroll?: boolean) {
    if (!this.element || this.index === undefined || !this.nodes || !this.items) {
      return
    }

    // 确保索引有效
    this.index = Math.max(0, Math.min(index, this.items.length - 1))

    // 更新选中状态
    for (let i = 0; i < this.nodes.length; i++) {
      if (i === this.index) {
        this.nodes[i].setAttribute('data-active', 'true')
      } else {
        this.nodes[i].removeAttribute('data-active')
      }
    }

    // 滚动到视图
    if (scroll && this.nodes[this.index]) {
      scrollIntoView(this.nodes[this.index], {
        block: 'center',
        scrollMode: 'if-needed',
        boundary: (parent) => parent !== this.element,
      })
    }
  }

  private render() {
    if (!this.element || this.index === undefined || !this.items) {
      return
    }

    // 清空内容
    while (this.element.firstChild) {
      this.element.removeChild(this.element.firstChild)
    }

    // 确保索引有效
    this.index = Math.max(0, Math.min(this.index, Math.max(0, this.items.length - 1)))

    if (this.items.length) {
      this.nodes = []

      for (let i = 0; i < this.items.length; i++) {
        const item = this.items[i]

        if (item === '|') {
          const divider = document.createElement('div')
          divider.classList.add('slash-menu-divider')
          this.nodes.push(divider)
        } else {
          const button = document.createElement('button')
          button.classList.add('slash-menu-button')
          button.setAttribute('type', 'button')

          // 图标
          if (item.icon) {
            const icon = document.createElement('div')
            icon.classList.add('slash-menu-button-icon')
            icon.innerHTML = item.icon
            button.appendChild(icon)
          }

          // 名称
          const name = document.createElement('div')
          name.classList.add('slash-menu-button-name')
          name.textContent = item.name
          button.appendChild(name)

          // 快捷键
          if (item.shortcut) {
            const shortcut = document.createElement('div')
            shortcut.classList.add('slash-menu-button-shortcut')
            shortcut.textContent = item.shortcut
              .replace(/mod/i, navigator.userAgent.includes('Mac') ? '⌘' : 'Ctrl')
              .replace(/ctrl|control/i, 'Ctrl')
              .replace(/cmd|command/i, '⌘')
              .replace(/shift/i, 'Shift')
              .replace(/alt|option/i, 'Alt')
            button.appendChild(shortcut)
          }

          // 设置选中状态
          if (i === this.index) {
            button.setAttribute('data-active', 'true')
          }

          // 添加事件监听
          button.addEventListener('click', () => {
            if (item.action) {
              item.action(this.editor)
            }
          })

          button.addEventListener('mouseover', () => {
            if (this.index !== i) {
              this.selectItem(i)
            }
          })

          this.nodes.push(button)
        }
      }

      this.element.append(...this.nodes)

      // 滚动到选中项
      if (this.nodes[this.index]) {
        scrollIntoView(this.nodes[this.index], {
          block: 'center',
          scrollMode: 'if-needed',
          boundary: (parent) => parent !== this.element,
        })
      }
    } else {
      // 显示空状态
      const empty = document.createElement('div')
      empty.classList.add('slash-menu-empty')
      empty.textContent = this.options.dictionary?.empty || '未找到结果'
      this.element.appendChild(empty)
    }

    this.show()
  }

  private updatePosition(clientRect: () => DOMRect) {
    if (!this.element) return

    const rect = clientRect()
    this.element.style.position = 'fixed'
    this.element.style.left = `${rect.left}px`
    this.element.style.top = `${rect.bottom + 8}px`
    this.element.style.zIndex = '10000'
  }

  private show() {
    if (this.element) {
      this.element.style.display = 'block'
    }
  }

  private hide() {
    if (this.element) {
      this.element.style.display = 'none'
    }
  }

  private cleanup() {
    if (this.element) {
      this.element.remove()
      this.element = undefined
    }
    this.index = undefined
    this.items = undefined
    this.nodes = undefined
  }
}
