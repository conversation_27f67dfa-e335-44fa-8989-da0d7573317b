# Design Document

## Overview

This design addresses the task list nesting issue in the TipTap editor where users cannot create nested lists (ordered/unordered) within task items. The current implementation incorrectly interprets list markers like '1.' as task item creation triggers instead of allowing proper list nesting within task content.

The solution involves modifying the task item extension's input handling, keyboard shortcuts, and content parsing to properly support nested list structures while maintaining task list functionality.

## Architecture

### Current Architecture Analysis

The current TipTap editor uses:
- **CustomTaskItem**: Extended from `@tiptap/extension-task-item` with custom node view
- **TaskList**: Standard TipTap task list extension
- **OrderedList/BulletList**: Standard TipTap list extensions
- **ListItem**: Standard TipTap list item extension

### Proposed Architecture Changes

1. **Enhanced Task Item Extension**: Modify the CustomTaskItem to properly handle nested content
2. **Input Rule Modifications**: Update input rules to distinguish between task creation and list creation contexts
3. **Keyboard Shortcut Enhancements**: Improve keyboard navigation for nested structures
4. **Content Model Updates**: Ensure proper content model definitions for nested structures

## Components and Interfaces

### 1. Enhanced CustomTaskItem Extension

**Location**: `web/src/components/tiptap/extensions/task-item/index.ts`

**Key Modifications**:
- Update content model to explicitly allow nested lists
- Modify input rules to prevent interference with list creation
- Enhance keyboard shortcuts for proper navigation
- Maintain existing checkbox functionality

**Interface Changes**:
```typescript
interface TaskItemOptions {
  nested: boolean
  onReadOnlyChecked?: (node: any, checked: boolean) => boolean
  HTMLAttributes: Record<string, any>
  // New options for nested list support
  allowNestedLists: boolean
  nestedListTypes: string[]
}
```

### 2. Input Rule Manager

**New Component**: `web/src/components/tiptap/extensions/task-item/InputRuleManager.ts`

**Purpose**: Handle input rule conflicts between task items and nested lists

**Key Functions**:
- `createTaskItemInputRules()`: Generate task-specific input rules
- `createNestedListInputRules()`: Generate nested list input rules with context awareness
- `resolveInputRuleConflicts()`: Determine appropriate action based on cursor context

### 3. Keyboard Navigation Handler

**New Component**: `web/src/components/tiptap/extensions/task-item/KeyboardHandler.ts`

**Purpose**: Manage keyboard shortcuts for nested list navigation within tasks

**Key Functions**:
- `handleTabIndentation()`: Manage Tab/Shift+Tab for list indentation
- `handleEnterKey()`: Control Enter behavior in nested lists
- `handleBackspace()`: Manage backspace behavior for list item removal

### 4. Content Model Validator

**New Component**: `web/src/components/tiptap/extensions/task-item/ContentValidator.ts`

**Purpose**: Ensure proper content structure validation

**Key Functions**:
- `validateNestedStructure()`: Validate nested list structures
- `sanitizeContent()`: Clean up malformed nested content
- `preserveTaskStructure()`: Maintain task integrity during edits

## Data Models

### Enhanced Task Item Node Schema

```typescript
const TaskItemNode = {
  name: 'taskItem',
  content: 'paragraph block*', // Allow paragraphs and block elements (including lists)
  defining: true,
  attrs: {
    checked: {
      default: false,
    },
  },
  parseHTML: [
    {
      tag: 'li[data-type="taskItem"]',
      getAttrs: (dom) => ({
        checked: dom.getAttribute('data-checked') === 'true',
      }),
    },
  ],
  renderHTML: ({ node, HTMLAttributes }) => [
    'li',
    mergeAttributes(HTMLAttributes, {
      'data-type': 'taskItem',
      'data-checked': node.attrs.checked,
    }),
    0,
  ],
}
```

### Nested List Context Model

```typescript
interface NestedListContext {
  isInTaskItem: boolean
  taskItemPos: number
  currentListType: 'bulletList' | 'orderedList' | null
  currentListLevel: number
  parentTaskChecked: boolean
}
```

## Error Handling

### Input Rule Conflicts

**Problem**: List markers ('1.', '-', '*') being interpreted as task creation
**Solution**: Context-aware input rule processing that checks cursor position relative to task items

**Implementation**:
```typescript
const resolveInputConflict = (state: EditorState, match: RegExpMatchArray) => {
  const { $from } = state.selection
  const taskItem = $from.node(-1)
  
  if (taskItem && taskItem.type.name === 'taskItem') {
    // Inside task item - create nested list
    return createNestedList(match)
  } else {
    // Outside task item - create new task or regular list
    return createTaskOrList(match)
  }
}
```

### Keyboard Navigation Edge Cases

**Problem**: Tab/Enter behavior conflicts between task and list navigation
**Solution**: Hierarchical keyboard shortcut handling with context priority

**Implementation**:
```typescript
const keyboardShortcuts = {
  Tab: ({ editor, state }) => {
    const context = getNestedListContext(state)
    
    if (context.isInTaskItem && context.currentListType) {
      return indentNestedListItem(editor, context)
    }
    
    return false // Let other extensions handle
  }
}
```

### Content Structure Validation

**Problem**: Malformed nested structures breaking task functionality
**Solution**: Content validation and auto-repair mechanisms

**Implementation**:
```typescript
const validateAndRepair = (doc: Node) => {
  doc.descendants((node, pos) => {
    if (node.type.name === 'taskItem') {
      return validateTaskItemContent(node, pos)
    }
  })
}
```

## Testing Strategy

### Unit Tests

1. **Input Rule Tests**
   - Test list marker input within task items
   - Test list marker input outside task items
   - Test edge cases with mixed content

2. **Keyboard Navigation Tests**
   - Test Tab/Shift+Tab indentation in nested lists
   - Test Enter key behavior in various contexts
   - Test Backspace behavior for list item removal

3. **Content Model Tests**
   - Test nested list creation and modification
   - Test task checkbox functionality with nested content
   - Test copy/paste operations with nested structures

### Integration Tests

1. **Editor Behavior Tests**
   - Test complete user workflows for nested list creation
   - Test interaction between different extension types
   - Test undo/redo functionality with nested structures

2. **Performance Tests**
   - Test editor performance with deeply nested structures
   - Test memory usage with large nested task lists
   - Test rendering performance with complex nested content

### User Acceptance Tests

1. **Workflow Tests**
   - Test creating ordered lists within task items
   - Test creating unordered lists within task items
   - Test mixed nested content scenarios

2. **Edge Case Tests**
   - Test copy/paste operations
   - Test drag and drop with nested content
   - Test markdown import/export with nested structures

## Implementation Approach

### Phase 1: Core Extension Modifications
- Modify CustomTaskItem extension content model
- Update input rules to handle context-aware list creation
- Implement basic keyboard navigation improvements

### Phase 2: Advanced Navigation Features
- Implement comprehensive keyboard shortcut handling
- Add content validation and repair mechanisms
- Enhance copy/paste functionality

### Phase 3: Polish and Optimization
- Optimize performance for nested structures
- Add comprehensive error handling
- Implement user experience improvements

### Integration Points

1. **Extension Registration**: Update `web/src/utils/tiptap.ts` to configure enhanced task item
2. **Editor Core**: Ensure proper extension loading in `EditorCore.ts`
3. **Event Handling**: Update event managers to handle nested list events
4. **Styling**: Update CSS to properly style nested lists within tasks

This design maintains backward compatibility while adding robust nested list support within task items, addressing all requirements while preserving existing functionality.