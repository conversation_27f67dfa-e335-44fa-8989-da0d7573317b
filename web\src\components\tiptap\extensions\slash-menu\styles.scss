// 斜杠菜单样式
.slash-menu {
  white-space: nowrap;
  pointer-events: all;
  width: 20rem;
  max-height: 15rem;
  padding: 0.25rem;
  overflow-x: hidden;
  overflow-y: auto;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  background-color: var(--bg-color, white);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  z-index: 10000;

  &-empty {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 0.875rem;
    height: 2.25rem;
    padding: 0 0.75rem;
    color: var(--text-muted, #6b7280);
  }

  &-divider {
    display: block;
    height: 1px;
    margin: 0.25rem -0.25rem;
    background-color: var(--border-color, #e5e7eb);
  }

  &-button {
    appearance: none;
    user-select: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    line-height: 1;
    font-weight: 500;
    font-size: 0.875rem;
    border: none;
    outline: none;
    width: 100%;
    height: 2.25rem;
    padding: 0.375rem 0.5rem;
    border-radius: 0.375rem;
    background-color: transparent;
    color: var(--text-color, #374151);
    transition: all 0.15s ease;

    &:hover,
    &:focus,
    &[data-active] {
      color: var(--text-active, #1f2937);
      background-color: var(--bg-hover, #f3f4f6);
    }

    &-icon {
      margin-left: 0.25rem;
      margin-right: 0.75rem;
      width: 1rem;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 1rem;
        height: 1rem;
      }
    }

    &-name {
      flex-grow: 1;
      text-align: start;
    }

    &-shortcut {
      color: var(--text-muted, #6b7280);
      font-family: var(--font-mono, 'Fira Code', monospace);
      font-size: 0.625rem;
      letter-spacing: 0.2em;
      text-transform: uppercase;
    }
  }
}

// 占位符样式
.slash-menu-placeholder::before {
  content: attr(data-placeholder);
  display: block;
  pointer-events: none;
  padding-left: 0.125rem;
  height: 0;
  opacity: 0;
  color: var(--text-muted, #6b7280);
  font-size: calc(1em - 0.1em);
  transition: opacity 0.15s ease;
}

.ProseMirror-focused .slash-menu-placeholder::before {
  opacity: 1;
}

.ProseMirror[contenteditable='false'] .slash-menu-placeholder::before {
  opacity: 0;
}

// 暗色主题支持
.dark-theme {
  .slash-menu {
    --bg-color: #1f2937;
    --border-color: #374151;
    --text-color: #f9fafb;
    --text-active: #ffffff;
    --text-muted: #9ca3af;
    --bg-hover: #374151;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -2px rgba(0, 0, 0, 0.2);
  }

  .slash-menu-placeholder::before {
    color: var(--text-muted, #9ca3af);
  }
}

// 滚动条样式
.slash-menu::-webkit-scrollbar {
  width: 5px;
}

.slash-menu::-webkit-scrollbar-track {
  background: transparent;
}

.slash-menu::-webkit-scrollbar-thumb {
  background: var(--border-color, #e5e7eb);
  border-radius: 5px;
}

.slash-menu * {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color, #e5e7eb) transparent;
}
